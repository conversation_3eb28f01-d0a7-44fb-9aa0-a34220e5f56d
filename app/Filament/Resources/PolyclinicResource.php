<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PolyclinicResource\Pages;
use App\Models\Polyclinic;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;

class PolyclinicResource extends Resource
{
    protected static ?string $model = Polyclinic::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Translations')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Français')
                            ->schema([
                                TextInput::make('fr.name')
                                    ->label('Nom (FR)')
                                    ->required()
                                    ->maxLength(255),
                                Textarea::make('fr.description')
                                    ->label('Description (FR)')
                                    ->columnSpanFull(),
                            ]),
                        Forms\Components\Tabs\Tab::make('English')
                            ->schema([
                                TextInput::make('en.name')
                                    ->label('Name (EN)')
                                    ->required()
                                    ->maxLength(255),
                                Textarea::make('en.description')
                                    ->label('Description (EN)')
                                    ->columnSpanFull(),
                            ]),
                        Forms\Components\Tabs\Tab::make('العربية')
                            ->schema([
                                TextInput::make('ar.name')
                                    ->label('الاسم (AR)')
                                    ->required()
                                    ->maxLength(255),
                                Textarea::make('ar.description')
                                    ->label('الوصف (AR)')
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePolyclinics::route('/'),
        ];
    }
}
