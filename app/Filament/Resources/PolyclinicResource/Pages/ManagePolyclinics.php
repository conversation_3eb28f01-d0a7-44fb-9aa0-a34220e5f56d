<?php

namespace App\Filament\Resources\PolyclinicResource\Pages;

use App\Filament\Resources\PolyclinicResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManagePolyclinics extends ManageRecords
{
    protected static string $resource = PolyclinicResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
