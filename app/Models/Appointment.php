<?php

namespace App\Models;

//use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Appointment extends Model
{
    //use Translatable;
    use HasFactory;
    //public $translatedAttributes = ['name'];
    public $fillable= ['name','email','phone','notes','doctor_id','polyclinic_id','type','appointment'];

    public function doctor()
    {
        return $this->belongsTo(Doctor::class,'doctor_id');
    }

    public function polyclinic()
    {
        return $this->belongsTo(Polyclinic::class,'polyclinic_id');
    }
}
