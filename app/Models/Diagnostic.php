<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Diagnostic extends Model
{
    use HasFactory;
    protected $fillable =['doctor_id','patient_id','date','review_date','diagnosis','medicine'];

    public function Doctor()
    {
        return $this->belongsTo(Doctor::class,'doctor_id'); //doctor_id is the foreign key in the diagnostic table
    }

    public function Patient()
    {
        return $this->belongsTo(Patient::class,'patient_id');
    }
}
