<?php

namespace App\Models;

use Astrotomic\Translatable\Translatable;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Polyclinic extends Model
{
    use Translatable; // 2. To add translation methods
    protected $fillable =['name','description'];
    // 3. To define which attributes needs to be translated
    public $translatedAttributes = ['name','description'];
    use HasFactory;

    public function doctors()
    {
        return $this->hasMany(Doctor::class);
    }

    // Accesseurs pour Filament
    public function getFrAttribute()
    {
        return [
            'name' => $this->translate('fr')->name ?? '',
            'description' => $this->translate('fr')->description ?? '',
        ];
    }

    public function getEnAttribute()
    {
        return [
            'name' => $this->translate('en')->name ?? '',
            'description' => $this->translate('en')->description ?? '',
        ];
    }

    public function getArAttribute()
    {
        return [
            'name' => $this->translate('ar')->name ?? '',
            'description' => $this->translate('ar')->description ?? '',
        ];
    }

    // Mutateurs pour Filament
    public function setFrAttribute($value)
    {
        if (is_array($value)) {
            $this->translateOrNew('fr')->name = $value['name'] ?? '';
            $this->translateOrNew('fr')->description = $value['description'] ?? '';
        }
    }

    public function setEnAttribute($value)
    {
        if (is_array($value)) {
            $this->translateOrNew('en')->name = $value['name'] ?? '';
            $this->translateOrNew('en')->description = $value['description'] ?? '';
        }
    }

    public function setArAttribute($value)
    {
        if (is_array($value)) {
            $this->translateOrNew('ar')->name = $value['name'] ?? '';
            $this->translateOrNew('ar')->description = $value['description'] ?? '';
        }
    }
}
