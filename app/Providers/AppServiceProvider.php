<?php

namespace App\Providers;

use Filament\Facades\Filament;
use Filament\Navigation\UserMenuItem;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Filament::serving(function () {
        $current = app()->getLocale();
        $next = $current === 'fr' ? 'en' : ($current === 'en' ? 'ar' : 'fr');

        Filament::registerUserMenuItems([
            'locale-switcher' => UserMenuItem::make()
                ->label('Langue : ' . strtoupper($current))
                ->url(LaravelLocalization::getLocalizedURL($next))
                ->icon('heroicon-o-language'),
        ]);
    });
    }
}
