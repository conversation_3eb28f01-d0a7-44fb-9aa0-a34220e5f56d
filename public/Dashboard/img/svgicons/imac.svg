<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 607.97 503.62"><defs><linearGradient id="linear-gradient" x1="260.12" y1="307.56" x2="387.38" y2="307.56" gradientTransform="matrix(0.99, 0.08, 0, 1, -2.65, 28.13)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#010101" stop-opacity="0"/><stop offset="0.95" stop-color="#010101"/></linearGradient><linearGradient id="linear-gradient-2" x1="313.4" y1="294.01" x2="391.76" y2="317.89" gradientTransform="matrix(0.99, 0.08, 0, 1, -2.65, 28.13)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff" stop-opacity="0"/><stop offset="0.95" stop-color="#fff"/></linearGradient><linearGradient id="linear-gradient-3" x1="287.24" y1="421.91" x2="303.13" y2="222.45" gradientTransform="matrix(1.01, -0.08, 0, 1, 1.5, -20.02)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-4" x1="273.36" y1="-89.9" x2="393.54" y2="434.52" gradientTransform="matrix(0.99, 0.16, 0, 1, -4.12, 47.81)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-5" x1="299.61" y1="120.13" x2="340.58" y2="288.32" gradientTransform="matrix(0.99, 0.16, 0, 1, -4.12, 47.81)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-6" x1="305.57" y1="175.07" x2="359.76" y2="276.78" gradientTransform="matrix(0.99, 0.16, 0, 1, -4.12, 47.81)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-7" x1="309.32" y1="174.61" x2="361.77" y2="273.05" gradientTransform="matrix(0.99, 0.16, 0, 1, -4.12, 47.81)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-8" x1="484.32" y1="327.6" x2="554.39" y2="327.6" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-9" x1="409.71" y1="316.52" x2="284.7" y2="531.16" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-10" x1="276.56" y1="390.26" x2="286.19" y2="390.26" gradientTransform="matrix(0.7, 0.75, -0.68, 0.73, 360.34, -67.19)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-11" x1="421.07" y1="290.26" x2="530.97" y2="543.24" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-12" x1="429.38" y1="286.65" x2="539.28" y2="539.63" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-13" x1="437.78" y1="283" x2="547.68" y2="535.99" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-14" x1="368.67" y1="313.02" x2="478.57" y2="566.01" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-15" x1="410.7" y1="294.76" x2="520.59" y2="547.75" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-16" x1="338.23" y1="326.25" x2="448.12" y2="579.23" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-17" x1="399.24" y1="299.74" x2="509.14" y2="552.73" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-18" x1="301.09" y1="342.38" x2="410.99" y2="595.36" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-19" x1="326.65" y1="331.28" x2="436.55" y2="584.26" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-20" x1="315.89" y1="335.95" x2="425.79" y2="588.93" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-21" x1="304.63" y1="340.84" x2="414.53" y2="593.82" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-22" x1="374.82" y1="310.35" x2="484.72" y2="563.33" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-23" x1="363.74" y1="315.16" x2="473.64" y2="568.15" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-24" x1="352.47" y1="320.06" x2="462.37" y2="573.04" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-25" x1="397.63" y1="300.44" x2="507.53" y2="553.43" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-26" x1="386.13" y1="305.44" x2="496.02" y2="558.42" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-27" x1="405.15" y1="297.18" x2="515.04" y2="550.16" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-28" x1="351.51" y1="320.47" x2="461.41" y2="573.46" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-29" x1="387.4" y1="304.88" x2="497.3" y2="557.87" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-30" x1="411.04" y1="294.62" x2="520.94" y2="547.6" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-31" x1="399.39" y1="299.68" x2="509.29" y2="552.66" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-32" x1="375.17" y1="310.2" x2="485.07" y2="563.18" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-33" x1="363.59" y1="315.23" x2="473.49" y2="568.21" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-34" x1="423.5" y1="289.2" x2="533.4" y2="542.19" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-35" x1="298.32" y1="343.58" x2="408.22" y2="596.57" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-36" x1="411.87" y1="294.25" x2="521.77" y2="547.24" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-37" x1="422.93" y1="289.45" x2="532.83" y2="542.44" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-38" x1="292.77" y1="345.99" x2="402.66" y2="598.98" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-39" x1="283.75" y1="349.91" x2="393.65" y2="602.89" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-40" x1="304.43" y1="340.93" x2="414.33" y2="593.91" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-41" x1="341.2" y1="324.95" x2="451.1" y2="577.94" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-42" x1="292.13" y1="346.27" x2="402.03" y2="599.26" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-43" x1="339.39" y1="325.74" x2="449.29" y2="578.73" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-44" x1="316.09" y1="335.86" x2="425.99" y2="588.85" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-45" x1="327.75" y1="330.8" x2="437.65" y2="583.78" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-46" x1="280.39" y1="351.37" x2="390.28" y2="604.36" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-47" x1="314.8" y1="336.42" x2="424.7" y2="589.41" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-48" x1="314.57" y1="336.52" x2="424.47" y2="589.51" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-49" x1="307.28" y1="339.69" x2="417.18" y2="592.68" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-50" x1="326.05" y1="331.53" x2="435.95" y2="584.52" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-51" x1="295.18" y1="344.95" x2="405.07" y2="597.93" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-52" x1="317.78" y1="335.13" x2="427.68" y2="588.11" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-53" x1="306.48" y1="340.04" x2="416.38" y2="593.02" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-54" x1="337.36" y1="326.62" x2="447.26" y2="579.61" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-55" x1="393.79" y1="302.11" x2="503.69" y2="555.09" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-56" x1="405.07" y1="297.21" x2="514.97" y2="550.19" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-57" x1="427.57" y1="287.44" x2="537.47" y2="540.42" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-58" x1="329.1" y1="330.21" x2="438.99" y2="583.2" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-59" x1="348.63" y1="321.72" x2="458.53" y2="574.71" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-60" x1="359.9" y1="316.83" x2="469.8" y2="569.82" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-61" x1="370.98" y1="312.02" x2="480.88" y2="565" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-62" x1="382.28" y1="307.11" x2="492.18" y2="560.09" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-63" x1="329.89" y1="329.87" x2="439.79" y2="582.85" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-64" x1="359.69" y1="316.92" x2="469.59" y2="569.91" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-65" x1="370.77" y1="312.11" x2="480.67" y2="565.1" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-66" x1="393.58" y1="302.2" x2="503.48" y2="555.19" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-67" x1="348.42" y1="321.82" x2="458.32" y2="574.8" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-68" x1="382.07" y1="307.2" x2="491.97" y2="560.19" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-69" x1="337.14" y1="326.72" x2="447.04" y2="579.7" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-70" x1="318.59" y1="334.78" x2="428.48" y2="587.76" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-71" x1="325.84" y1="331.63" x2="435.73" y2="584.61" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-72" x1="396.81" y1="300.8" x2="506.71" y2="553.78" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-73" x1="351.64" y1="320.42" x2="461.54" y2="573.4" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-74" x1="417.2" y1="291.94" x2="527.09" y2="544.93" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-75" x1="374.02" y1="310.7" x2="483.92" y2="563.68" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-76" x1="362.72" y1="315.61" x2="472.62" y2="568.59" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-77" x1="340.37" y1="325.31" x2="450.27" y2="578.3" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-78" x1="385.53" y1="305.7" x2="495.43" y2="558.68" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-79" x1="408.91" y1="295.54" x2="518.81" y2="548.53" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-80" x1="383.35" y1="278.85" x2="459.13" y2="367.91" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-81" x1="389.99" y1="273.2" x2="465.77" y2="362.27" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-82" x1="396.67" y1="267.52" x2="472.46" y2="356.58" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-83" x1="341.98" y1="314.06" x2="417.76" y2="403.12" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-84" x1="375.55" y1="285.49" x2="451.33" y2="374.55" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-85" x1="317.66" y1="334.75" x2="393.44" y2="423.81" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-86" x1="366.4" y1="293.28" x2="442.18" y2="382.34" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-87" x1="290" y1="358.29" x2="365.78" y2="447.35" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-88" x1="308.41" y1="342.62" x2="384.2" y2="431.68" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-89" x1="299.82" y1="349.93" x2="375.6" y2="438.99" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-90" x1="290.83" y1="357.58" x2="366.61" y2="446.64" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-91" x1="352.56" y1="305.06" x2="428.34" y2="394.12" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-92" x1="343.71" y1="312.59" x2="419.49" y2="401.65" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-93" x1="334.7" y1="320.25" x2="410.49" y2="409.31" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-94" x1="370.78" y1="289.55" x2="446.56" y2="378.61" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-95" x1="361.58" y1="297.37" x2="437.37" y2="386.44" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-96" x1="375" y1="285.96" x2="450.79" y2="375.02" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-97" x1="337.13" y1="318.18" x2="412.92" y2="407.24" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-98" x1="365.8" y1="293.79" x2="441.58" y2="382.85" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-99" x1="384.68" y1="277.72" x2="460.46" y2="366.78" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-100" x1="375.37" y1="285.64" x2="451.16" y2="374.7" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-101" x1="356.03" y1="302.1" x2="431.81" y2="391.16" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-102" x1="346.78" y1="309.97" x2="422.56" y2="399.04" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-103" x1="387.78" y1="275.08" x2="463.56" y2="364.15" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-104" x1="289.68" y1="358.56" x2="365.46" y2="447.62" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-105" x1="383.84" y1="278.44" x2="459.62" y2="367.5" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-106" x1="390.04" y1="273.16" x2="465.83" y2="362.22" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-107" x1="290.21" y1="358.11" x2="365.99" y2="447.17" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-108" x1="281.5" y1="365.52" x2="357.29" y2="454.58" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-109" x1="299.52" y1="350.18" x2="375.31" y2="439.25" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-110" x1="325.7" y1="327.91" x2="401.48" y2="416.97" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-111" x1="286.5" y1="361.26" x2="362.29" y2="450.32" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-112" x1="327.45" y1="326.42" x2="403.23" y2="415.48" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-113" x1="308.84" y1="342.26" x2="384.62" y2="431.32" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-114" x1="318.15" y1="334.33" x2="393.94" y2="423.39" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-115" x1="280.3" y1="366.54" x2="356.09" y2="455.6" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-116" x1="300.95" y1="348.97" x2="376.73" y2="438.03" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-117" x1="302.65" y1="347.52" x2="378.44" y2="436.58" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-118" x1="298.6" y1="350.97" x2="374.39" y2="440.03" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-119" x1="309.94" y1="341.32" x2="385.72" y2="430.38" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-120" x1="290.63" y1="357.75" x2="366.41" y2="446.81" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-121" x1="308.68" y1="342.39" x2="384.47" y2="431.45" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-122" x1="299.66" y1="350.07" x2="375.45" y2="439.13" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-123" x1="318.97" y1="333.64" x2="394.75" y2="422.7" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-124" x1="364.05" y1="295.28" x2="439.83" y2="384.34" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-125" x1="373.06" y1="287.61" x2="448.84" y2="376.67" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-126" x1="389.39" y1="273.72" x2="465.17" y2="362.78" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-127" x1="317.72" y1="334.7" x2="393.51" y2="423.76" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-128" x1="327.98" y1="325.97" x2="403.76" y2="415.03" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-129" x1="336.98" y1="318.31" x2="412.76" y2="407.37" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-130" x1="345.83" y1="310.78" x2="421.61" y2="399.84" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-131" x1="354.86" y1="303.1" x2="430.64" y2="392.16" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-132" x1="316.67" y1="335.6" x2="392.45" y2="424.66" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-133" x1="338.69" y1="316.85" x2="414.48" y2="405.92" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-134" x1="347.54" y1="309.32" x2="423.33" y2="398.38" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-135" x1="365.76" y1="293.82" x2="441.55" y2="382.88" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-136" x1="329.69" y1="324.51" x2="405.48" y2="413.58" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-137" x1="356.57" y1="301.64" x2="432.36" y2="390.7" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-138" x1="320.68" y1="332.18" x2="396.47" y2="421.24" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-139" x1="307.63" y1="343.28" x2="383.42" y2="432.34" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-140" x1="311.65" y1="339.86" x2="387.44" y2="428.92" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-141" x1="371.81" y1="288.67" x2="447.6" y2="377.73" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-142" x1="335.73" y1="319.37" x2="411.52" y2="408.43" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-143" x1="384.63" y1="277.77" x2="460.41" y2="366.83" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-144" x1="353.61" y1="304.16" x2="429.4" y2="393.22" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-145" x1="344.58" y1="311.84" x2="420.37" y2="400.9" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-146" x1="326.73" y1="327.03" x2="402.52" y2="416.09" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-147" x1="362.8" y1="296.34" x2="438.59" y2="385.4" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-148" x1="379.79" y1="281.89" x2="455.57" y2="370.95" gradientTransform="matrix(1.02, 0.07, 0, 1, 5.76, 18.98)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-149" x1="700.11" y1="349.46" x2="700.25" y2="346.24" gradientTransform="matrix(1.23, 0, 0.87, 0.8, -558.15, 156.86)" xlink:href="#linear-gradient-2"/><linearGradient id="linear-gradient-150" x1="700.65" y1="349.49" x2="700.79" y2="346.26" gradientTransform="matrix(1.23, 0, 0.87, 0.8, -558.15, 156.86)" xlink:href="#linear-gradient-2"/></defs><title>Imac</title><path d="M472.71,84c-16.59-16.25-31.94-34-50.81-47.55C381.26,7.3,325.3.92,279.13,20.19c-40.67,17-71.56,51.16-97.65,86.69s-49.11,73.78-80.8,104.42c-21.74,21-48.05,39.19-60.42,66.8s-8,60.91,7.08,87.11,39.94,45.78,67.09,59.1c16.47,8.08,34.33,14.33,47.75,26.83,9.81,9.13,16.59,21,25.73,30.81C215,511,259.58,517.56,298.53,509.76s74.23-27.63,110-44.92,74.27-32.56,113.93-30.11c17.54,1.08,35.07,5.63,52.5,3.31,33.66-4.49,60-35.58,65-69.16s-8.5-68-30.19-94.15c-9-10.89-19.62-20.81-25.94-33.47-15-30-2.37-72.41-29.07-96.29C529.79,122.62,497.58,108.4,472.71,84Z" transform="translate(-33.06 -9.17)" fill="#285cf7" opacity="0.24" style="isolation:isolate"/><path d="M491,370.71c-2.49.89-5.3,2.54-5.31,5.18,0,2.86,3.2,4.47,5.89,5.41l39.09,13.64a37.31,37.31,0,0,0,9.91,2.47c3.41.21,7-.63,9.5-3a4.55,4.55,0,0,0,1.65-3.41c-.12-2-2.06-3.27-3.79-4.23q-14.75-8.19-30.08-15.26C508.21,367.06,501.29,367,491,370.71Z" transform="translate(-33.06 -9.17)" fill="#285cf7" opacity="0.24" style="isolation:isolate"/><path d="M313.74,451.15c9.47,2.31,18.38,7.49,28.13,7.57,7.87.06,15.34-3.23,22.39-6.74,36.73-18.28,70.9-44.76,111.57-50.19-9.56-13.8-28.91-15.84-45.7-15.53a403.45,403.45,0,0,0-102,15.13C304.57,408,271.74,422,255.39,441.17,269.75,454.57,296.11,446.84,313.74,451.15Z" transform="translate(-33.06 -9.17)" fill="#285cf7" opacity="0.24" style="isolation:isolate"/><path d="M160.72,339.79c-11.88,2-24.09,5.64-32.68,14.1s-12.19,23-5.26,32.83c7.85,11.19,24,11.47,37.66,10.94q37.22-1.46,74.46.46c12.84.66,25.79,1.61,38.51-.29,10.93-1.63,21.44-5.34,31.86-9L381,362c3.1-1.09,6.47-2.41,8-5.31,3.23-6-3.56-12.38-9.63-15.46-31.72-16.09-65-18.25-99-10.59C241,339.48,200.35,333,160.72,339.79Z" transform="translate(-33.06 -9.17)" fill="#285cf7" opacity="0.24" style="isolation:isolate"/><path d="M291.73,331l-32.28,11.21c-5.49,8.87-2.17,18,.53,19.9l33.61,23.74A19,19,0,0,0,310,388.35L379.08,366c3.72-1.21,3.42-5.75-.47-7.05L295,331A5.2,5.2,0,0,0,291.73,331Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M291.73,331l-27.57,2.39c-3.08,1-13.61,19.9-6,28.21l35.45,24.25A19,19,0,0,0,310,388.35L379.08,366c3.72-1.21,3.42-5.75-.47-7.05L295,331A5.2,5.2,0,0,0,291.73,331Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient)"/><path d="M173.48,151.1V351.77c0,6.07,6.65,11.12,12.49,9.49l251.89-71a6.88,6.88,0,0,0,5.17-6.79V77.67c0-6.07-6.65-11.12-12.49-9.49l-247,69.68A13.42,13.42,0,0,0,173.48,151.1Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M319.36,310.7,274,325.4c-3.09,1-6.68,2-8.25,6.91l-5.56,17.63a11.08,11.08,0,0,0,4.48,12.69L293.59,383A19,19,0,0,0,310,385.48l69.06-22.37c3.72-1.21,3.42-5.75-.47-7.05l-51.54-17.21a5.61,5.61,0,0,1-4.16-5.18l-.29-22.93A5.27,5.27,0,0,0,319.36,310.7Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M319.36,310.7,274,325.4c-3.09,1-6.68,2-8.25,6.91l-5.56,17.63a11.08,11.08,0,0,0,4.48,12.69L293.59,383A19,19,0,0,0,310,385.48l69.06-22.37c3.72-1.21,3.42-5.75-.47-7.05l-51.54-17.21a5.61,5.61,0,0,1-4.16-5.18l-.29-22.93A5.27,5.27,0,0,0,319.36,310.7Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-2)"/><path d="M262.42,361.34l64.65-22.49a5.62,5.62,0,0,1-4.16-5.18l-.39-7L260.58,343.8S256.58,355.73,262.42,361.34Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-3)"/><path d="M178.06,156.12V356.79c0,6.07,6.64,11.12,12.49,9.49l251.89-71a6.89,6.89,0,0,0,5.16-6.8V82.69c0-6.07-6.64-11.12-12.49-9.49l-247,69.68A13.41,13.41,0,0,0,178.06,156.12Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M178.06,156.12V356.79c0,6.07,6.64,11.12,12.49,9.49l251.89-71a6.89,6.89,0,0,0,5.16-6.8V82.69c0-6.07-6.64-11.12-12.49-9.49l-247,69.68A13.41,13.41,0,0,0,178.06,156.12Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-4)"/><path d="M178.06,356.79c0,6.07,6.64,11.12,12.49,9.49l251.89-71a6.89,6.89,0,0,0,5.16-6.8V261.39L178.06,337.34Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M178.06,356.79c0,6.07,6.64,11.12,12.49,9.49l251.89-71a6.89,6.89,0,0,0,5.16-6.8V261.39L178.06,337.34Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-5)"/><g id="c5Uefy"><path d="M322,310.31l-.25.28a4.13,4.13,0,0,0-.83,1.25,2.63,2.63,0,0,0,.31,2.78,1.68,1.68,0,0,0,1,.6l.17,0a2.17,2.17,0,0,1-.07.24,9.88,9.88,0,0,1-1.33,2.58,4.88,4.88,0,0,1-.52.59,1.71,1.71,0,0,1-1.34.56l-.87-.08a2.36,2.36,0,0,0-.75,0,3.21,3.21,0,0,0-1.08.46,4.51,4.51,0,0,1-.84.44,1.38,1.38,0,0,1-.8.05,1.87,1.87,0,0,1-.86-.5,6.16,6.16,0,0,1-1.2-1.73,6,6,0,0,1-.61-2.08,5.79,5.79,0,0,1,.32-2.64,4.45,4.45,0,0,1,1-1.61,3.05,3.05,0,0,1,1.64-.88,4.89,4.89,0,0,1,1.32-.05l.54,0a1.8,1.8,0,0,0,.86-.2c.31-.17.62-.34.94-.5a3.75,3.75,0,0,1,1.18-.4,2.16,2.16,0,0,1,1.63.37c.15.1.28.23.42.34Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-6)"/><path d="M317.14,310.37a3.73,3.73,0,0,1,2.42-3.54.94.94,0,0,1,0,.47,3.87,3.87,0,0,1-1.2,2.4,2.34,2.34,0,0,1-1,.58Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-7)"/></g><path d="M501.55,374.09c3.71,2.3,44.56,23.31,47.72,24.27s8.14,1.19,16.71-2.75,5.8-8.15,5.8-8.15L548.6,376.25l-38.7-8.38s-5.84,1.16-8.33,3A1.86,1.86,0,0,0,501.55,374.09Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M501.55,374.09c3.71,2.3,44.56,23.31,47.72,24.27s8.14,1.19,16.71-2.75,5.8-8.15,5.8-8.15L548.6,376.25l-38.7-8.38s-5.84,1.16-8.33,3A1.86,1.86,0,0,0,501.55,374.09Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-8)"/><path d="M570.81,390.69a4,4,0,0,0-.24-6c-22.28-19.37-44.71-21.6-45.69-21.71,0,0-6.41-.83-15,2.88a34.51,34.51,0,0,0-9,5.26,1.16,1.16,0,0,0,.61,2c7,1.61,25.48,5.09,44.62,22.3a6.87,6.87,0,0,0,4.68,1.8,39.81,39.81,0,0,0,12.52-2.41A23.66,23.66,0,0,0,570.81,390.69Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><g opacity="0.17"><path d="M571.94,390.43l-.1.2a3.86,3.86,0,0,1-.35.54,4.34,4.34,0,0,1-.31.36,4.89,4.89,0,0,1-.41.37c-.3.25-.66.54-1.07.83a21.7,21.7,0,0,1-3.14,1.86,33.49,33.49,0,0,1-4.42,1.79,43.64,43.64,0,0,1-5.51,1.43c-1,.19-2,.35-3.11.46-.53.06-1.08.1-1.63.13a16,16,0,0,1-1.71,0,7.37,7.37,0,0,1-3.48-1.2,6.85,6.85,0,0,1-.76-.57l-.68-.59-1.36-1.17c-1.83-1.52-3.74-3-5.72-4.43s-4-2.77-6-4-4.07-2.44-6.07-3.52-4-2.06-5.86-2.89a51.86,51.86,0,0,0-5.46-2c-3.45-1.08-6.36-1.91-8.39-2.47l-3.2-.87h0a.26.26,0,0,1-.19-.3.23.23,0,0,1,.28-.16l3.22.78c2.05.52,5,1.24,8.46,2.28a56.86,56.86,0,0,1,5.59,1.94c1.94.82,3.94,1.78,6,2.86s4.08,2.26,6.14,3.53,4.1,2.63,6.1,4.05,3.92,2.95,5.78,4.51l1.35,1.18.67.58a6.24,6.24,0,0,0,.65.5,6.32,6.32,0,0,0,3,1,14.12,14.12,0,0,0,1.59,0c.53,0,1.06-.06,1.58-.11,1-.09,2.06-.23,3-.4a43,43,0,0,0,5.42-1.32,37.84,37.84,0,0,0,4.39-1.65,22.55,22.55,0,0,0,3.12-1.73,9.36,9.36,0,0,0,1.74-1.4,3.27,3.27,0,0,0,.32-.47l.1-.18h0a.24.24,0,0,1,.33-.08A.25.25,0,0,1,571.94,390.43Z" transform="translate(-33.06 -9.17)" fill="#5c5c5c"/></g><polygon points="283.64 413.5 261.05 422 265.12 426.51 287.71 418.01 283.64 413.5" fill="#95928f"/><path d="M437.59,368.32,291.81,421.88c-1,.36-1,3,0,3.53l53.7,36.93a5.44,5.44,0,0,0,4.4.32l144.68-54.45c1-.37,1.17-2.91.19-3.42l-55-36.31A2.68,2.68,0,0,0,437.59,368.32Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M437.59,368.32,291.81,421.88c-1,.36-1,3,0,3.53l53.7,36.93a5.44,5.44,0,0,0,4.4.32l144.68-54.45c1-.37,1.17-2.91.19-3.42l-55-36.31A2.68,2.68,0,0,0,437.59,368.32Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-9)"/><ellipse cx="294.52" cy="428.72" rx="6.39" ry="8" transform="translate(-245.73 304.25) rotate(-42.71)" fill="#285cf7"/><path d="M437.59,366.57,291.8,421.44a1.22,1.22,0,0,0,0,2.23l53.7,36.93a5.43,5.43,0,0,0,4.4.31l144.68-54.45a1.22,1.22,0,0,0,0-2.24L439.8,366.73A2.76,2.76,0,0,0,437.59,366.57Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><g opacity="0.2"><path d="M494.58,404.22l-28.69-19.63-156.69,51,36.3,25a5.43,5.43,0,0,0,4.4.31l144.68-54.45A1.22,1.22,0,0,0,494.58,404.22Z" transform="translate(-33.06 -9.17)" fill="#f1ebe7"/></g><ellipse cx="293.25" cy="429.39" rx="4.38" ry="5.48" transform="translate(-246.52 303.57) rotate(-42.71)" fill="url(#linear-gradient-10)"/><path d="M462.91,410.89c-.77.29-.87.89-.23,1.32l.73.51a3.41,3.41,0,0,0,2.8.29l4.68-1.76c.7-.26.79-.8.21-1.19l-.85-.58a3.42,3.42,0,0,0-2.79-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-11)"/><path d="M471,407.87c-.61.23-.7.71-.18,1.06l.88.61a3.65,3.65,0,0,0,3,.32l4.91-1.85c.61-.23.7-.71.18-1.06l-.88-.61a3.62,3.62,0,0,0-3-.31Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-12)"/><path d="M480.7,406.39a3.48,3.48,0,0,0,2.83.3l4.54-1.71c.49-.19.56-.57.15-.86l-1.13-.77a3.48,3.48,0,0,0-2.83-.3l-4.68,1.76c-.42.16-.48.48-.12.72Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-13)"/><path d="M386.9,434.65c-.82.31-.93.94-.24,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l44.08-16.58c.82-.31.93-.95.24-1.42l-5.33-3.64a3.39,3.39,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-14)"/><path d="M448.9,411.32c-.82.31-.94.95-.25,1.42l5.33,3.65a3.35,3.35,0,0,0,2.74.29l5.31-2c.82-.31.93-.94.24-1.41l-5.33-3.65a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-15)"/><path d="M374.71,439.24c-.82.31-.93.94-.25,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l6.75-2.54c.82-.31.93-.94.25-1.41L384.19,437a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-16)"/><path d="M436.43,416c-.82.31-.93.94-.24,1.41l5.33,3.65a3.33,3.33,0,0,0,2.73.29l7-2.64c.82-.31.93-1,.25-1.42l-5.29-3.61a3.35,3.35,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-17)"/><path d="M345.66,442.13a3.29,3.29,0,0,0-2.73-.31L331.77,446c-.82.31-.93.95-.25,1.42l4.3,2.94a3.35,3.35,0,0,0,2.74.29l11-4.14c.82-.31.93-.95.25-1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-18)"/><path d="M363.43,443.48c-.82.31-.93.95-.24,1.41l5.33,3.65a3.33,3.33,0,0,0,2.73.29l5.83-2.19c.82-.31.94-.94.25-1.41L372,441.58a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-19)"/><path d="M352.9,447.44c-.83.31-.94.95-.25,1.42l5.33,3.65a3.35,3.35,0,0,0,2.74.29l5.09-1.92c.82-.3.93-.94.24-1.41l-5.33-3.65a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-20)"/><path d="M340.61,452.07c-.82.3-.93.94-.24,1.41l5.33,3.65a3.34,3.34,0,0,0,2.74.29l6.83-2.57c.82-.31.93-1,.25-1.42l-5.33-3.64a3.39,3.39,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-21)"/><path d="M410.37,399.28a2.67,2.67,0,0,0-1.69.2L403,401.6c-.82.31-.93.95-.25,1.41l3,2.05a6.74,6.74,0,0,0,1.36.68,2.94,2.94,0,0,0,1.58-.18l5.63-2.12c.82-.31.93-.95.25-1.41l-3-2C411,399.53,410.89,399.38,410.37,399.28Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-22)"/><path d="M391.59,405.91c-.82.31-.93.94-.25,1.41l3.22,2.2a3.3,3.3,0,0,0,1.33.55,3.1,3.1,0,0,0,1.44-.23l6-2.26c.83-.31.94-.94.25-1.41l-3.25-2.23a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-23)"/><path d="M387.63,407.7a3.23,3.23,0,0,0-1.47.25l-6,2.25c-.82.31-.93.94-.25,1.41l3.26,2.23a3.33,3.33,0,0,0,2.73.29l6-2.24c.82-.31.93-.94.24-1.41L389,408.32A4.92,4.92,0,0,0,387.63,407.7Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-24)"/><path d="M437.68,394.66c.82-.31.93-.94.25-1.41L434.67,391a3.35,3.35,0,0,0-2.74-.29L426,393c-.83.31-.94.94-.25,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-25)"/><path d="M422.3,394.84a2.47,2.47,0,0,0-1.7.15l-6.55,2.47c-.82.31-.93.94-.25,1.41l3.25,2.22a2.78,2.78,0,0,0,1.41.48,2.44,2.44,0,0,0,1.34-.18l6.45-2.43c.82-.31.93-.94.25-1.41l-2.9-2A5.27,5.27,0,0,0,422.3,394.84Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-26)"/><path d="M436,397.46c-.82.31-.93,1-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29l6.58-2.47c.82-.31.93-1,.25-1.42l-4.25-2.9a3.39,3.39,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-27)"/><path d="M384.85,397.74c.83-.31.94-.95.25-1.42l-1.47-1a3.34,3.34,0,0,0-2.74-.29l-7.36,2.77c-.82.31-.94.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-28)"/><path d="M421.31,384c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-7.52,2.83c-.82.31-.93.95-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-29)"/><path d="M444.93,375.14c.82-.31.93-.95.24-1.42l-1.46-1a3.39,3.39,0,0,0-2.74-.3L434.14,375c-.82.31-.93.95-.25,1.42l1.47,1a3.39,3.39,0,0,0,2.74.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-30)"/><path d="M432.65,379.76c.82-.31.93-.95.25-1.42l-1.47-1a3.39,3.39,0,0,0-2.74-.3l-5.89,2.22c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-31)"/><path d="M408.34,388.9c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.38,2.4c-.82.31-.93.94-.24,1.41l1.47,1a3.34,3.34,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-32)"/><path d="M396.52,393.35c.82-.31.93-1,.24-1.41l-1.47-1a3.33,3.33,0,0,0-2.73-.29L386.34,393c-.82.31-.93.94-.24,1.41l1.46,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-33)"/><path d="M455.07,405.38a3.43,3.43,0,0,0,1.41.54,2.48,2.48,0,0,0,1.42-.16l20.5-7.71c.82-.31.93-.95.24-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29l-20.47,7.7c-.83.31-.94.95-.25,1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-34)"/><path d="M345.82,438.55c.82-.31.93-.95.25-1.42l-4.22-2.91a3.34,3.34,0,0,0-2.74-.29L323,440c-.82.3-.93.94-.25,1.41l4.26,2.91a2.42,2.42,0,0,0,1.26.49,4.37,4.37,0,0,0,1.58-.24Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-35)"/><path d="M436.38,385.53a3.23,3.23,0,0,0,2.72.32L453,380.64c.82-.31.94-.95.25-1.42l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-13.65,5.14c-.82.31-.94,1-.27,1.45Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-36)"/><path d="M451.64,387.89a3,3,0,0,0,1.35.64,4.59,4.59,0,0,0,1.73-.28,8.82,8.82,0,0,1,1.85-.69,3.92,3.92,0,0,1,1.91.93l6,4.1a3.33,3.33,0,0,0,2.73.29l2.36-.89c.82-.3.93-.94.24-1.41l-12-8.25A3.39,3.39,0,0,0,455,382l-6.2,2.33c-.82.31-.93.95-.24,1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-37)"/><path d="M324.8,420.33c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.94.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-38)"/><path d="M316.13,427.1a3.39,3.39,0,0,0-2.74-.3l-6.25,2.36c-.82.3-.93.94-.24,1.41l3.49,2.39a3.33,3.33,0,0,0,2.73.29l6.25-2.35c.82-.31.93-.95.25-1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-39)"/><path d="M336.62,415.88c.83-.3.94-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-40)"/><path d="M368.73,414.51c-.82.31-.94.94-.25,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-.95.24-1.41l-3.25-2.23a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-41)"/><path d="M331.54,429.82a3.35,3.35,0,0,0-2.74-.29l-13.62,5.13c-.82.31-.93.94-.25,1.41l3.26,2.23a3.34,3.34,0,0,0,2.74.29l13.62-5.13c.82-.3.93-.94.24-1.41Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-42)"/><path d="M372,402.56c.82-.31.93-.95.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.32,2.38c-.82.31-.93.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-43)"/><path d="M348.45,411.44c.82-.31.93-.95.24-1.42l-1.46-1a3.39,3.39,0,0,0-2.74-.3l-6.38,2.4c-.82.31-.93.95-.24,1.42l1.46,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-44)"/><path d="M360.27,407c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.39c-.83.31-.94.95-.25,1.42l1.47,1a3.39,3.39,0,0,0,2.74.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-45)"/><path d="M313.77,424.15s.12-.33-.57-.8l-1.44-1a3.35,3.35,0,0,0-2.74-.29l-7.89,3c-.82.31-.93.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29l7.83-2.95C313.74,424.49,313.76,424.15,313.77,424.15Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-46)"/><path d="M355.65,437.33a3.63,3.63,0,0,0-1.5.26l-5.93,2.23c-.82.31-.93.95-.24,1.42l4.29,2.94a3.35,3.35,0,0,0,2.74.29l5.9-2.22c.82-.31.93-.95.25-1.42L357,438A6,6,0,0,0,355.65,437.33Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-47)"/><path d="M357.21,434.26c.82-.3.93-.94.24-1.41l-4.24-2.91a3.39,3.39,0,0,0-2.74-.29l-5.94,2.24c-.82.31-.93.94-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-48)"/><path d="M343.32,425.63A6.46,6.46,0,0,0,342,425a3.11,3.11,0,0,0-1.57.21l-6.06,2.28c-.82.31-.93.94-.25,1.41l3.26,2.23a3.34,3.34,0,0,0,2.74.29l6-2.26c.82-.31.93-.95.25-1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-49)"/><path d="M359.57,435.55c-.82.31-.93.95-.24,1.42l4.28,2.93a3,3,0,0,0,1.16.54,4,4,0,0,0,1.59-.24l6-2.26c.82-.31.94-.95.25-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-50)"/><path d="M326.3,422.25a3,3,0,0,0-1.43.24l-6,2.26c-.82.31-.93.95-.25,1.42l3.49,2.39a3.35,3.35,0,0,0,2.74.29l6-2.26c.83-.31.94-.95.25-1.42l-3.38-2.32A4.42,4.42,0,0,0,326.3,422.25Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-51)"/><path d="M353.76,418c.82-.31.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.94,1-.27,1.44l3.37,2.4a3.27,3.27,0,0,0,2.72.31Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-52)"/><path d="M334.88,424.8a3.5,3.5,0,0,0,1.4-.26l6-2.27c.82-.31.93-.94.24-1.41l-3.48-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.95-.24,1.42l3.47,2.38A3.86,3.86,0,0,0,334.88,424.8Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-53)"/><path d="M371,431.24c-.82.31-.93.95-.25,1.42l4.29,2.93a3.39,3.39,0,0,0,2.74.3l6-2.27c.82-.31.93-.94.24-1.41l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-54)"/><path d="M428.27,409.71c-.83.31-.94.94-.25,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l6-2.25c.82-.3.93-.94.25-1.41l-4.3-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-55)"/><path d="M439.69,405.41c-.82.31-.93.95-.25,1.41l4.3,2.94a3.35,3.35,0,0,0,2.74.29l6-2.25c.82-.31.93-.95.25-1.42l-4.29-2.93a3.39,3.39,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-56)"/><path d="M467.85,404.62c-.87.33-1,1-.26,1.51l1.26.86a2.65,2.65,0,0,0,2.19.24l4.95-1.86c.82-.31.94-1,.25-1.43l-1.72-1.17a1.67,1.67,0,0,0-1.35-.15Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-57)"/><path d="M357.78,416.15a3.35,3.35,0,0,0,1.43-.24l6-2.26c.82-.31.93-.95.25-1.42L362,409.85a3.39,3.39,0,0,0-2.74-.3l-6,2.27c-.82.31-.93.94-.25,1.41l3.49,2.39A3.06,3.06,0,0,0,357.78,416.15Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-58)"/><path d="M382.5,426.93c-.82.31-.93.94-.25,1.41l4.3,2.94a3.33,3.33,0,0,0,2.73.29l6-2.24c.82-.3.93-.94.24-1.41L391.19,425a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-59)"/><path d="M393.9,422.64c-.82.31-.93.94-.25,1.41L398,427a3.33,3.33,0,0,0,2.73.29l6-2.25c.83-.31.94-.95.25-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-60)"/><path d="M412.32,416a3.05,3.05,0,0,0-1.4.24l-5.57,2.09c-.82.31-.93.95-.25,1.42l4.1,2.8a3.6,3.6,0,0,0,1.36.55,2.44,2.44,0,0,0,1.54-.11l5.6-2.11c.82-.31.93-.94.25-1.41l-4.26-2.91A3.52,3.52,0,0,0,412.32,416Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-61)"/><path d="M424.25,411.57a2.5,2.5,0,0,0-1.42.18l-6.47,2.44c-.82.31-.93.94-.25,1.41l4.3,2.94a3.35,3.35,0,0,0,2.74.29l6.45-2.43c.82-.3.93-.94.25-1.41l-4.19-2.87A3.49,3.49,0,0,0,424.25,411.57Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-62)"/><path d="M364.81,416.29a3.7,3.7,0,0,0-1.52.26l-6,2.27c-.82.31-.93.95-.24,1.42l3.16,2.16a2.47,2.47,0,0,0,1.29.56,2.39,2.39,0,0,0,1.53-.2l6-2.27c.82-.31.94-.94.25-1.41l-3.15-2.16A4.88,4.88,0,0,0,364.81,416.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-63)"/><path d="M395.39,419.54a2.37,2.37,0,0,0,1.52-.22L403,417c.82-.31.93-.94.25-1.41L399,412.72a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.94-.25,1.41l4,2.72A3,3,0,0,0,395.39,419.54Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-64)"/><path d="M414,412.9c.82-.31.93-.95.25-1.42l-4.25-2.9a3.33,3.33,0,0,0-2.73-.29l-5.57,2.09c-.82.31-.93.95-.25,1.42l4.25,2.9a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-65)"/><path d="M437.32,404.12c.82-.31.93-.94.25-1.41l-4.25-2.91a3.33,3.33,0,0,0-2.73-.29l-6,2.25c-.82.31-.93.94-.25,1.41l4.25,2.91a3.33,3.33,0,0,0,2.73.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-66)"/><path d="M391.53,421.35c.82-.31.93-.94.25-1.41L387.53,417a3.35,3.35,0,0,0-2.74-.29l-6,2.24c-.82.31-.93.94-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-67)"/><path d="M425.89,408.38c.82-.29.94-.9.25-1.37l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-6.46,2.43c-.82.31-.93.94-.24,1.41l4,2.74a3.44,3.44,0,0,0,2.74.33Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-68)"/><path d="M380.13,425.64c.82-.31.93-.95.25-1.41l-3.77-2.59a4.66,4.66,0,0,0-1.42-.82,2.38,2.38,0,0,0-1.63.15l-6.18,2.32c-.82.31-.93.95-.25,1.42l4.25,2.9a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-69)"/><path d="M345.8,423.13c-.82.31-.93.95-.24,1.42l3.25,2.23a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-.95.25-1.42l-3.26-2.23a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-70)"/><path d="M361,432.42a2.62,2.62,0,0,0,1.56-.17l6.1-2.3c.82-.31.93-.94.24-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.95-.25,1.42l3.92,2.68A10.27,10.27,0,0,0,361,432.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-71)"/><path d="M426.36,390.64l1.5-.56,6-2.26c.82-.3.93-.94.24-1.41L430.62,384a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.94-.24,1.41l3.46,2.37Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-72)"/><path d="M388.07,405.05c.82-.31.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.3-.93.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-73)"/><path d="M452.06,397.25a3.39,3.39,0,0,0,2.74.3l7-2.62c.82-.31.93-.95.24-1.42l-4.24-2.9a3.35,3.35,0,0,0-2.74-.29l-7,2.61c-.82.31-.93.95-.24,1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-74)"/><path d="M411,396.43c.82-.31.93-.95.24-1.42L408,392.78a6.25,6.25,0,0,0-1.36-.65,2.93,2.93,0,0,0-1.56.19l-6.51,2.44c-.82.31-.93.95-.24,1.42l3.48,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-75)"/><path d="M399.08,400.91c.83-.31.94-1,.25-1.42L396,397.22a4.41,4.41,0,0,0-1.29-.61,3.31,3.31,0,0,0-1.59.19l-5.59,2.11c-.82.31-.93.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-76)"/><path d="M369.22,411.83a3.1,3.1,0,0,0,1.44-.23l6-2.24c.82-.31.93-.95.25-1.42l-3.49-2.38a3.35,3.35,0,0,0-2.74-.29l-5.95,2.23c-.82.31-.93.95-.25,1.42l3.42,2.34A3.78,3.78,0,0,0,369.22,411.83Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-77)"/><path d="M422.41,392.13c.83-.31.94-.95.25-1.41l-3.49-2.39a3.34,3.34,0,0,0-2.74-.29l-6,2.25c-.82.3-.94.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-78)"/><path d="M449.12,390.36c.82-.31.93-.95.25-1.42l-3.26-2.23a3.36,3.36,0,0,0-2.73-.29l-6,2.26c-.82.31-.93.94-.25,1.41l3.26,2.23a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-79)"/><path d="M463.05,409.93c-.77.29-.87.89-.23,1.33l.73.5a3.45,3.45,0,0,0,2.8.3l4.68-1.77c.7-.26.79-.79.21-1.19l-.84-.58a3.45,3.45,0,0,0-2.8-.3Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M471.14,406.91c-.61.24-.7.71-.18,1.07l.88.6a3.66,3.66,0,0,0,3,.32l4.91-1.85c.61-.23.7-.71.18-1.06l-.88-.61a3.61,3.61,0,0,0-3-.31Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M480.84,405.43a3.48,3.48,0,0,0,2.83.3l4.54-1.71c.5-.18.56-.57.15-.85l-1.13-.78a3.48,3.48,0,0,0-2.83-.3l-4.68,1.76c-.42.16-.47.49-.12.73Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M387.05,433.69c-.82.31-.94.95-.25,1.42l5.33,3.64a3.39,3.39,0,0,0,2.74.3L439,422.46c.82-.31.93-.94.24-1.41l-5.33-3.65a3.34,3.34,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M449,410.37c-.82.31-.93.94-.25,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l5.31-2c.82-.3.93-.94.25-1.41l-5.34-3.65a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M374.85,438.28c-.82.31-.93.94-.25,1.41l5.34,3.65a3.33,3.33,0,0,0,2.73.29l6.75-2.53c.82-.31.93-1,.25-1.42L384.34,436a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M436.57,415.06c-.82.31-.93.94-.24,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l7-2.64c.82-.31.93-.94.25-1.41l-5.29-3.62a3.35,3.35,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M345.8,441.17a3.29,3.29,0,0,0-2.73-.31l-11.16,4.2c-.82.3-.93.94-.24,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l11-4.14c.82-.31.94-.95.25-1.42Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M363.57,442.52c-.82.31-.93,1-.24,1.42l5.33,3.65a3.38,3.38,0,0,0,2.74.29l5.83-2.2c.82-.31.93-.94.24-1.41l-5.33-3.65a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M353,446.49c-.82.31-.93.94-.25,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l5.09-1.91c.82-.31.93-.95.24-1.42l-5.33-3.65a3.34,3.34,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M340.75,451.11c-.82.31-.93.94-.24,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l6.83-2.57c.82-.31.93-.94.25-1.41l-5.33-3.65a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M410.51,398.33a2.66,2.66,0,0,0-1.69.19l-5.64,2.12c-.82.31-.93.95-.25,1.42l3,2a6.74,6.74,0,0,0,1.36.68,2.94,2.94,0,0,0,1.58-.18l5.63-2.12c.82-.31.93-.94.25-1.41l-3-2C411.09,398.57,411,398.42,410.51,398.33Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M391.73,405c-.82.31-.93.95-.25,1.41l3.22,2.21a3.24,3.24,0,0,0,1.33.54,3.1,3.1,0,0,0,1.44-.23l6-2.25c.82-.31.93-.95.24-1.42L400.47,403a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M387.77,406.74a3.23,3.23,0,0,0-1.47.25l-6,2.25c-.82.31-.93.94-.25,1.41l3.26,2.23a3.34,3.34,0,0,0,2.74.29l5.95-2.24c.82-.31.93-.94.24-1.41l-3.14-2.15A5.15,5.15,0,0,0,387.77,406.74Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M437.82,393.7c.82-.31.93-.94.25-1.41l-3.26-2.23a3.34,3.34,0,0,0-2.74-.29l-6,2.25c-.82.31-.94,1-.25,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M422.45,393.88a2.46,2.46,0,0,0-1.7.15l-6.56,2.47c-.82.31-.93.94-.24,1.41l3.24,2.22a2.81,2.81,0,0,0,1.41.48,2.44,2.44,0,0,0,1.34-.18l6.45-2.43c.82-.31.94-.94.25-1.41l-2.9-2A5.24,5.24,0,0,0,422.45,393.88Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M436.18,396.5c-.82.31-.94.95-.25,1.42l4.24,2.9a3.35,3.35,0,0,0,2.74.29l6.58-2.47c.82-.31.93-.95.25-1.42l-4.25-2.9a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M385,396.78c.82-.31.93-.95.24-1.41l-1.47-1a3.33,3.33,0,0,0-2.73-.29l-7.37,2.77c-.82.31-.93,1-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M421.45,383.06c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29L410,383.18c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M445.07,374.18c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29L434.28,374c-.82.3-.93.94-.25,1.41l1.47,1a3.39,3.39,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M432.79,378.8c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-5.89,2.21c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M408.48,387.94c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.94.95-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M396.66,392.39c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29L386.48,392c-.82.31-.93.94-.24,1.41l1.47,1a3.33,3.33,0,0,0,2.73.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M455.21,404.42a3.79,3.79,0,0,0,1.41.55,2.57,2.57,0,0,0,1.43-.17l20.49-7.71c.82-.31.93-.95.24-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29l-20.47,7.71c-.82.31-.94.94-.25,1.41Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M346,437.59c.82-.31.94-.95.25-1.42L342,433.26a3.33,3.33,0,0,0-2.73-.29L323.12,439c-.82.31-.93.94-.25,1.41l4.26,2.92a2.49,2.49,0,0,0,1.26.48,4.13,4.13,0,0,0,1.58-.24Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M436.52,384.57a3.27,3.27,0,0,0,2.72.33l13.86-5.22c.83-.31.94-.95.25-1.42l-3.49-2.38a3.39,3.39,0,0,0-2.74-.3l-13.65,5.14c-.82.31-.94,1-.27,1.45Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M451.79,386.93a3,3,0,0,0,1.34.64,4.59,4.59,0,0,0,1.73-.28,8.82,8.82,0,0,1,1.85-.69,4,4,0,0,1,1.91.93l6,4.1a3.34,3.34,0,0,0,2.74.29l2.35-.88c.82-.31.93-1,.24-1.42l-12-8.24a3.39,3.39,0,0,0-2.74-.3L449,383.41c-.82.31-.94.95-.25,1.42Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M324.94,419.37c.82-.3.93-.94.25-1.41l-1.47-1a3.39,3.39,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M316.27,426.14a3.35,3.35,0,0,0-2.74-.29l-6.24,2.35c-.83.31-.94.94-.25,1.41l3.49,2.39a3.34,3.34,0,0,0,2.74.29l6.24-2.35c.82-.31.94-.95.25-1.41Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M336.77,414.93c.82-.31.93-.95.24-1.42l-1.47-1a3.37,3.37,0,0,0-2.73-.3l-6.38,2.4c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M368.87,413.55c-.82.31-.93.95-.25,1.42l3.26,2.22a3.33,3.33,0,0,0,2.73.29l6-2.26c.82-.31.93-.94.24-1.41l-3.25-2.23a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M331.68,428.86a3.39,3.39,0,0,0-2.74-.29l-13.62,5.13c-.82.31-.93.94-.25,1.41l3.26,2.23a3.35,3.35,0,0,0,2.74.29l13.62-5.12c.82-.31.93-.95.24-1.42Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M372.18,401.6c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.32,2.38c-.82.31-.93.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M348.59,410.48c.82-.31.93-1,.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.38,2.39c-.82.31-.93.95-.24,1.42l1.47,1a3.37,3.37,0,0,0,2.73.3Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M360.41,406c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.94.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M313.91,423.19s.12-.33-.56-.8l-1.45-1a3.35,3.35,0,0,0-2.74-.29l-7.89,3c-.82.31-.93.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29l7.83-2.94C313.88,423.54,313.9,423.19,313.91,423.19Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M355.79,436.37a3.55,3.55,0,0,0-1.5.27l-5.93,2.23c-.82.31-.93.94-.24,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l5.9-2.22c.82-.31.93-1,.25-1.41l-4.13-2.83A6.56,6.56,0,0,0,355.79,436.37Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M357.35,433.31c.82-.31.93-.95.24-1.42l-4.24-2.9a3.35,3.35,0,0,0-2.74-.29l-5.94,2.23c-.82.31-.93.94-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M343.46,424.68a6.53,6.53,0,0,0-1.34-.68,3.14,3.14,0,0,0-1.58.21l-6.06,2.28c-.82.31-.93.94-.25,1.41l3.26,2.23a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-1,.25-1.41Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M359.72,434.6c-.82.3-.94.94-.25,1.41l4.28,2.93a3.07,3.07,0,0,0,1.16.54,4,4,0,0,0,1.59-.24l6-2.26c.82-.31.93-1,.24-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M326.45,421.29a3,3,0,0,0-1.44.24l-6,2.27c-.82.31-.93.94-.24,1.41l3.48,2.39a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-.95.24-1.42l-3.38-2.31A4.4,4.4,0,0,0,326.45,421.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M353.9,417c.82-.31.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.94,1-.26,1.44L345,419a3.27,3.27,0,0,0,2.72.31Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M335,423.84a3.5,3.5,0,0,0,1.4-.26l6-2.27c.82-.3.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.95-.24,1.42l3.47,2.38A3.5,3.5,0,0,0,335,423.84Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M371.18,430.28c-.82.31-.93,1-.25,1.42l4.3,2.94a3.33,3.33,0,0,0,2.73.29l6-2.27c.82-.3.93-.94.24-1.41l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M428.41,408.75c-.82.31-.94.95-.25,1.42l4.29,2.93a3.39,3.39,0,0,0,2.74.3l6-2.25c.82-.31.93-.95.25-1.42l-4.3-2.94a3.34,3.34,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M439.83,404.45c-.82.31-.93.95-.24,1.42l4.29,2.94a3.39,3.39,0,0,0,2.74.29l6-2.26c.82-.31.94-.94.25-1.41l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M468,403.66c-.88.33-1,1-.27,1.52L469,406a2.69,2.69,0,0,0,2.19.23l4.95-1.86c.83-.31.94-1,.25-1.43l-1.72-1.17a1.63,1.63,0,0,0-1.35-.14Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M357.93,415.19a3.32,3.32,0,0,0,1.42-.24l6-2.26c.82-.31.94-.95.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.94-.25,1.41l3.49,2.39A3.07,3.07,0,0,0,357.93,415.19Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M382.64,426c-.82.31-.93.95-.25,1.41l4.3,2.94a3.35,3.35,0,0,0,2.74.29l5.95-2.23c.82-.31.93-.95.24-1.42L391.33,424a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M394,421.68c-.82.31-.93.95-.25,1.42l4.3,2.93a3.39,3.39,0,0,0,2.74.3l6-2.26c.82-.31.93-.95.24-1.42l-4.29-2.94a3.39,3.39,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M412.46,415a3.05,3.05,0,0,0-1.4.24l-5.57,2.09c-.82.31-.93.95-.25,1.42l4.1,2.81a3.68,3.68,0,0,0,1.36.54,2.44,2.44,0,0,0,1.54-.11l5.6-2.11c.82-.3.93-.94.25-1.41l-4.26-2.91A3.47,3.47,0,0,0,412.46,415Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M424.39,410.61a2.56,2.56,0,0,0-1.42.19l-6.47,2.43c-.82.31-.93.95-.24,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l6.45-2.42c.82-.31.94-.95.25-1.42l-4.19-2.87A3.67,3.67,0,0,0,424.39,410.61Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M365,415.33a3.64,3.64,0,0,0-1.52.27l-6,2.26c-.83.31-.94.95-.25,1.42l3.16,2.16a2.47,2.47,0,0,0,1.29.56,2.39,2.39,0,0,0,1.53-.2l6-2.27c.83-.3.94-.94.25-1.41L366.26,416A4.88,4.88,0,0,0,365,415.33Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M395.53,418.58a2.38,2.38,0,0,0,1.53-.21l6.06-2.29c.82-.3.93-.94.25-1.41l-4.25-2.91a3.39,3.39,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.95-.24,1.42l4,2.72A3,3,0,0,0,395.53,418.58Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M414.13,411.94c.82-.31.94-.94.25-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-5.57,2.09c-.82.31-.93.95-.24,1.42l4.24,2.9a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M437.46,403.16c.82-.31.94-.94.25-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-6,2.25c-.82.31-.93.95-.25,1.42l4.25,2.9a3.34,3.34,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M391.67,420.39c.82-.31.93-.94.25-1.41l-4.25-2.91a3.34,3.34,0,0,0-2.74-.29L379,418c-.82.31-.93,1-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M426,407.43c.82-.29.94-.91.25-1.38L422,403.14a3.35,3.35,0,0,0-2.74-.29l-6.45,2.43c-.83.31-.94,1-.25,1.41l4,2.74a3.45,3.45,0,0,0,2.75.33Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M380.27,424.68c.82-.31.93-.94.25-1.41l-3.77-2.58a4.53,4.53,0,0,0-1.42-.83,2.38,2.38,0,0,0-1.63.15l-6.18,2.32c-.82.31-.93.95-.25,1.42l4.25,2.9a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M345.94,422.18c-.82.31-.93.94-.24,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-1,.25-1.42l-3.25-2.23a3.39,3.39,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M361.15,431.46a2.62,2.62,0,0,0,1.56-.17l6.1-2.3c.82-.3.93-.94.25-1.41l-4.25-2.91a3.35,3.35,0,0,0-2.74-.29l-6,2.27c-.82.3-.93.94-.25,1.41l3.92,2.68A9.12,9.12,0,0,0,361.15,431.46Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M426.5,389.68l1.5-.56,6-2.25c.82-.31.93-.95.25-1.42l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29L422,385c-.82.31-.93.94-.24,1.41l3.46,2.37Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M388.21,404.09c.82-.31.93-.94.25-1.41L385,400.29a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M452.2,396.3a3.35,3.35,0,0,0,2.74.29l7-2.62c.82-.31.93-.94.24-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-7,2.62c-.82.31-.93.94-.24,1.41Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M411.13,395.47c.82-.31.93-.94.25-1.41l-3.26-2.23a5.82,5.82,0,0,0-1.36-.66,2.93,2.93,0,0,0-1.56.19l-6.51,2.45c-.82.3-.93.94-.24,1.41l3.49,2.39a3.33,3.33,0,0,0,2.73.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M399.23,400c.82-.31.93-.95.24-1.42l-3.32-2.27a4.41,4.41,0,0,0-1.29-.61,3.24,3.24,0,0,0-1.59.2l-5.59,2.1c-.82.31-.93.95-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M369.36,410.87a3.1,3.1,0,0,0,1.44-.23l6-2.24c.82-.31.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-5.95,2.24c-.82.31-.93.94-.25,1.41L368,410.3A3.78,3.78,0,0,0,369.36,410.87Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M422.56,391.17c.82-.31.93-.94.24-1.41l-3.49-2.39a3.33,3.33,0,0,0-2.73-.29l-6,2.25c-.82.31-.93.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M449.26,389.4c.82-.31.94-.95.25-1.42l-3.25-2.22a3.39,3.39,0,0,0-2.74-.3l-6,2.26c-.82.31-.93.95-.24,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="#285cf7"/><path d="M463.05,409.93c-.77.29-.87.89-.23,1.33l.73.5a3.45,3.45,0,0,0,2.8.3l4.68-1.77c.7-.26.79-.79.21-1.19l-.84-.58a3.45,3.45,0,0,0-2.8-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-80)"/><path d="M471.14,406.91c-.61.24-.7.71-.18,1.07l.88.6a3.66,3.66,0,0,0,3,.32l4.91-1.85c.61-.23.7-.71.18-1.06l-.88-.61a3.61,3.61,0,0,0-3-.31Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-81)"/><path d="M480.84,405.43a3.48,3.48,0,0,0,2.83.3l4.54-1.71c.5-.18.56-.57.15-.85l-1.13-.78a3.48,3.48,0,0,0-2.83-.3l-4.68,1.76c-.42.16-.47.49-.12.73Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-82)"/><path d="M387.05,433.69c-.82.31-.94.95-.25,1.42l5.33,3.64a3.39,3.39,0,0,0,2.74.3L439,422.46c.82-.31.93-.94.24-1.41l-5.33-3.65a3.34,3.34,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-83)"/><path d="M449,410.37c-.82.31-.93.94-.25,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l5.31-2c.82-.3.93-.94.25-1.41l-5.34-3.65a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-84)"/><path d="M374.85,438.28c-.82.31-.93.94-.25,1.41l5.34,3.65a3.33,3.33,0,0,0,2.73.29l6.75-2.53c.82-.31.93-1,.25-1.42L384.34,436a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-85)"/><path d="M436.57,415.06c-.82.31-.93.94-.24,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l7-2.64c.82-.31.93-.94.25-1.41l-5.29-3.62a3.35,3.35,0,0,0-2.74-.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-86)"/><path d="M345.8,441.17a3.29,3.29,0,0,0-2.73-.31l-11.16,4.2c-.82.3-.93.94-.24,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l11-4.14c.82-.31.94-.95.25-1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-87)"/><path d="M363.57,442.52c-.82.31-.93,1-.24,1.42l5.33,3.65a3.38,3.38,0,0,0,2.74.29l5.83-2.2c.82-.31.93-.94.24-1.41l-5.33-3.65a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-88)"/><path d="M353,446.49c-.82.31-.93.94-.25,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l5.09-1.91c.82-.31.93-.95.24-1.42l-5.33-3.65a3.34,3.34,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-89)"/><path d="M340.75,451.11c-.82.31-.93.94-.24,1.41l5.33,3.65a3.35,3.35,0,0,0,2.74.29l6.83-2.57c.82-.31.93-.94.25-1.41l-5.33-3.65a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-90)"/><path d="M410.51,398.33a2.66,2.66,0,0,0-1.69.19l-5.64,2.12c-.82.31-.93.95-.25,1.42l3,2a6.74,6.74,0,0,0,1.36.68,2.94,2.94,0,0,0,1.58-.18l5.63-2.12c.82-.31.93-.94.25-1.41l-3-2C411.09,398.57,411,398.42,410.51,398.33Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-91)"/><path d="M391.73,405c-.82.31-.93.95-.25,1.41l3.22,2.21a3.24,3.24,0,0,0,1.33.54,3.1,3.1,0,0,0,1.44-.23l6-2.25c.82-.31.93-.95.24-1.42L400.47,403a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-92)"/><path d="M387.77,406.74a3.23,3.23,0,0,0-1.47.25l-6,2.25c-.82.31-.93.94-.25,1.41l3.26,2.23a3.34,3.34,0,0,0,2.74.29l5.95-2.24c.82-.31.93-.94.24-1.41l-3.14-2.15A5.15,5.15,0,0,0,387.77,406.74Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-93)"/><path d="M437.82,393.7c.82-.31.93-.94.25-1.41l-3.26-2.23a3.34,3.34,0,0,0-2.74-.29l-6,2.25c-.82.31-.94,1-.25,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-94)"/><path d="M422.45,393.88a2.46,2.46,0,0,0-1.7.15l-6.56,2.47c-.82.31-.93.94-.24,1.41l3.24,2.22a2.81,2.81,0,0,0,1.41.48,2.44,2.44,0,0,0,1.34-.18l6.45-2.43c.82-.31.94-.94.25-1.41l-2.9-2A5.24,5.24,0,0,0,422.45,393.88Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-95)"/><path d="M436.18,396.5c-.82.31-.94.95-.25,1.42l4.24,2.9a3.35,3.35,0,0,0,2.74.29l6.58-2.47c.82-.31.93-.95.25-1.42l-4.25-2.9a3.33,3.33,0,0,0-2.73-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-96)"/><path d="M385,396.78c.82-.31.93-.95.24-1.41l-1.47-1a3.33,3.33,0,0,0-2.73-.29l-7.37,2.77c-.82.31-.93,1-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-97)"/><path d="M421.45,383.06c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29L410,383.18c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-98)"/><path d="M445.07,374.18c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29L434.28,374c-.82.3-.93.94-.25,1.41l1.47,1a3.39,3.39,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-99)"/><path d="M432.79,378.8c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-5.89,2.21c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-100)"/><path d="M408.48,387.94c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.94.95-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-101)"/><path d="M396.66,392.39c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29L386.48,392c-.82.31-.93.94-.24,1.41l1.47,1a3.33,3.33,0,0,0,2.73.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-102)"/><path d="M455.21,404.42a3.79,3.79,0,0,0,1.41.55,2.57,2.57,0,0,0,1.43-.17l20.49-7.71c.82-.31.93-.95.24-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29l-20.47,7.71c-.82.31-.94.94-.25,1.41Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-103)"/><path d="M346,437.59c.82-.31.94-.95.25-1.42L342,433.26a3.33,3.33,0,0,0-2.73-.29L323.12,439c-.82.31-.93.94-.25,1.41l4.26,2.92a2.49,2.49,0,0,0,1.26.48,4.13,4.13,0,0,0,1.58-.24Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-104)"/><path d="M436.52,384.57a3.27,3.27,0,0,0,2.72.33l13.86-5.22c.83-.31.94-.95.25-1.42l-3.49-2.38a3.39,3.39,0,0,0-2.74-.3l-13.65,5.14c-.82.31-.94,1-.27,1.45Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-105)"/><path d="M451.79,386.93a3,3,0,0,0,1.34.64,4.59,4.59,0,0,0,1.73-.28,8.82,8.82,0,0,1,1.85-.69,4,4,0,0,1,1.91.93l6,4.1a3.34,3.34,0,0,0,2.74.29l2.35-.88c.82-.31.93-1,.24-1.42l-12-8.24a3.39,3.39,0,0,0-2.74-.3L449,383.41c-.82.31-.94.95-.25,1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-106)"/><path d="M324.94,419.37c.82-.3.93-.94.25-1.41l-1.47-1a3.39,3.39,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-107)"/><path d="M316.27,426.14a3.35,3.35,0,0,0-2.74-.29l-6.24,2.35c-.83.31-.94.94-.25,1.41l3.49,2.39a3.34,3.34,0,0,0,2.74.29l6.24-2.35c.82-.31.94-.95.25-1.41Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-108)"/><path d="M336.77,414.93c.82-.31.93-.95.24-1.42l-1.47-1a3.37,3.37,0,0,0-2.73-.3l-6.38,2.4c-.82.31-.93.95-.25,1.42l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-109)"/><path d="M368.87,413.55c-.82.31-.93.95-.25,1.42l3.26,2.22a3.33,3.33,0,0,0,2.73.29l6-2.26c.82-.31.93-.94.24-1.41l-3.25-2.23a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-110)"/><path d="M331.68,428.86a3.39,3.39,0,0,0-2.74-.29l-13.62,5.13c-.82.31-.93.94-.25,1.41l3.26,2.23a3.35,3.35,0,0,0,2.74.29l13.62-5.12c.82-.31.93-.95.24-1.42Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-111)"/><path d="M372.18,401.6c.82-.31.93-.94.25-1.41l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.32,2.38c-.82.31-.93.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-112)"/><path d="M348.59,410.48c.82-.31.93-1,.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.38,2.39c-.82.31-.93.95-.24,1.42l1.47,1a3.37,3.37,0,0,0,2.73.3Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-113)"/><path d="M360.41,406c.82-.31.93-.95.25-1.42l-1.47-1a3.35,3.35,0,0,0-2.74-.29l-6.37,2.4c-.82.31-.94.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-114)"/><path d="M313.91,423.19s.12-.33-.56-.8l-1.45-1a3.35,3.35,0,0,0-2.74-.29l-7.89,3c-.82.31-.93.94-.25,1.41l1.47,1a3.35,3.35,0,0,0,2.74.29l7.83-2.94C313.88,423.54,313.9,423.19,313.91,423.19Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-115)"/><path d="M355.79,436.37a3.55,3.55,0,0,0-1.5.27l-5.93,2.23c-.82.31-.93.94-.24,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l5.9-2.22c.82-.31.93-1,.25-1.41l-4.13-2.83A6.56,6.56,0,0,0,355.79,436.37Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-116)"/><path d="M357.35,433.31c.82-.31.93-.95.24-1.42l-4.24-2.9a3.35,3.35,0,0,0-2.74-.29l-5.94,2.23c-.82.31-.93.94-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-117)"/><path d="M343.46,424.68a6.53,6.53,0,0,0-1.34-.68,3.14,3.14,0,0,0-1.58.21l-6.06,2.28c-.82.31-.93.94-.25,1.41l3.26,2.23a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-1,.25-1.41Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-118)"/><path d="M359.72,434.6c-.82.3-.94.94-.25,1.41l4.28,2.93a3.07,3.07,0,0,0,1.16.54,4,4,0,0,0,1.59-.24l6-2.26c.82-.31.93-1,.24-1.42l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-119)"/><path d="M326.45,421.29a3,3,0,0,0-1.44.24l-6,2.27c-.82.31-.93.94-.24,1.41l3.48,2.39a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-.95.24-1.42l-3.38-2.31A4.4,4.4,0,0,0,326.45,421.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-120)"/><path d="M353.9,417c.82-.31.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.94,1-.26,1.44L345,419a3.27,3.27,0,0,0,2.72.31Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-121)"/><path d="M335,423.84a3.5,3.5,0,0,0,1.4-.26l6-2.27c.82-.3.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.95-.24,1.42l3.47,2.38A3.5,3.5,0,0,0,335,423.84Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-122)"/><path d="M371.18,430.28c-.82.31-.93,1-.25,1.42l4.3,2.94a3.33,3.33,0,0,0,2.73.29l6-2.27c.82-.3.93-.94.24-1.41l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-123)"/><path d="M428.41,408.75c-.82.31-.94.95-.25,1.42l4.29,2.93a3.39,3.39,0,0,0,2.74.3l6-2.25c.82-.31.93-.95.25-1.42l-4.3-2.94a3.34,3.34,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-124)"/><path d="M439.83,404.45c-.82.31-.93.95-.24,1.42l4.29,2.94a3.39,3.39,0,0,0,2.74.29l6-2.26c.82-.31.94-.94.25-1.41l-4.29-2.94a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-125)"/><path d="M468,403.66c-.88.33-1,1-.27,1.52L469,406a2.69,2.69,0,0,0,2.19.23l4.95-1.86c.83-.31.94-1,.25-1.43l-1.72-1.17a1.63,1.63,0,0,0-1.35-.14Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-126)"/><path d="M357.93,415.19a3.32,3.32,0,0,0,1.42-.24l6-2.26c.82-.31.94-.95.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.94-.25,1.41l3.49,2.39A3.07,3.07,0,0,0,357.93,415.19Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-127)"/><path d="M382.64,426c-.82.31-.93.95-.25,1.41l4.3,2.94a3.35,3.35,0,0,0,2.74.29l5.95-2.23c.82-.31.93-.95.24-1.42L391.33,424a3.35,3.35,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-128)"/><path d="M394,421.68c-.82.31-.93.95-.25,1.42l4.3,2.93a3.39,3.39,0,0,0,2.74.3l6-2.26c.82-.31.93-.95.24-1.42l-4.29-2.94a3.39,3.39,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-129)"/><path d="M412.46,415a3.05,3.05,0,0,0-1.4.24l-5.57,2.09c-.82.31-.93.95-.25,1.42l4.1,2.81a3.68,3.68,0,0,0,1.36.54,2.44,2.44,0,0,0,1.54-.11l5.6-2.11c.82-.3.93-.94.25-1.41l-4.26-2.91A3.47,3.47,0,0,0,412.46,415Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-130)"/><path d="M424.39,410.61a2.56,2.56,0,0,0-1.42.19l-6.47,2.43c-.82.31-.93.95-.24,1.41l4.29,2.94a3.35,3.35,0,0,0,2.74.29l6.45-2.42c.82-.31.94-.95.25-1.42l-4.19-2.87A3.67,3.67,0,0,0,424.39,410.61Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-131)"/><path d="M365,415.33a3.64,3.64,0,0,0-1.52.27l-6,2.26c-.83.31-.94.95-.25,1.42l3.16,2.16a2.47,2.47,0,0,0,1.29.56,2.39,2.39,0,0,0,1.53-.2l6-2.27c.83-.3.94-.94.25-1.41L366.26,416A4.88,4.88,0,0,0,365,415.33Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-132)"/><path d="M395.53,418.58a2.38,2.38,0,0,0,1.53-.21l6.06-2.29c.82-.3.93-.94.25-1.41l-4.25-2.91a3.39,3.39,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.95-.24,1.42l4,2.72A3,3,0,0,0,395.53,418.58Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-133)"/><path d="M414.13,411.94c.82-.31.94-.94.25-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-5.57,2.09c-.82.31-.93.95-.24,1.42l4.24,2.9a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-134)"/><path d="M437.46,403.16c.82-.31.94-.94.25-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-6,2.25c-.82.31-.93.95-.25,1.42l4.25,2.9a3.34,3.34,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-135)"/><path d="M391.67,420.39c.82-.31.93-.94.25-1.41l-4.25-2.91a3.34,3.34,0,0,0-2.74-.29L379,418c-.82.31-.93,1-.24,1.41l4.24,2.91a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-136)"/><path d="M426,407.43c.82-.29.94-.91.25-1.38L422,403.14a3.35,3.35,0,0,0-2.74-.29l-6.45,2.43c-.83.31-.94,1-.25,1.41l4,2.74a3.45,3.45,0,0,0,2.75.33Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-137)"/><path d="M380.27,424.68c.82-.31.93-.94.25-1.41l-3.77-2.58a4.53,4.53,0,0,0-1.42-.83,2.38,2.38,0,0,0-1.63.15l-6.18,2.32c-.82.31-.93.95-.25,1.42l4.25,2.9a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-138)"/><path d="M345.94,422.18c-.82.31-.93.94-.24,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29l6-2.26c.82-.31.93-1,.25-1.42l-3.25-2.23a3.39,3.39,0,0,0-2.74-.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-139)"/><path d="M361.15,431.46a2.62,2.62,0,0,0,1.56-.17l6.1-2.3c.82-.3.93-.94.25-1.41l-4.25-2.91a3.35,3.35,0,0,0-2.74-.29l-6,2.27c-.82.3-.93.94-.25,1.41l3.92,2.68A9.12,9.12,0,0,0,361.15,431.46Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-140)"/><path d="M426.5,389.68l1.5-.56,6-2.25c.82-.31.93-.95.25-1.42l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29L422,385c-.82.31-.93.94-.24,1.41l3.46,2.37Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-141)"/><path d="M388.21,404.09c.82-.31.93-.94.25-1.41L385,400.29a3.35,3.35,0,0,0-2.74-.29l-6,2.26c-.82.31-.93.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-142)"/><path d="M452.2,396.3a3.35,3.35,0,0,0,2.74.29l7-2.62c.82-.31.93-.94.24-1.41l-4.24-2.91a3.35,3.35,0,0,0-2.74-.29l-7,2.62c-.82.31-.93.94-.24,1.41Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-143)"/><path d="M411.13,395.47c.82-.31.93-.94.25-1.41l-3.26-2.23a5.82,5.82,0,0,0-1.36-.66,2.93,2.93,0,0,0-1.56.19l-6.51,2.45c-.82.3-.93.94-.24,1.41l3.49,2.39a3.33,3.33,0,0,0,2.73.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-144)"/><path d="M399.23,400c.82-.31.93-.95.24-1.42l-3.32-2.27a4.41,4.41,0,0,0-1.29-.61,3.24,3.24,0,0,0-1.59.2l-5.59,2.1c-.82.31-.93.95-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-145)"/><path d="M369.36,410.87a3.1,3.1,0,0,0,1.44-.23l6-2.24c.82-.31.93-.94.25-1.41l-3.49-2.39a3.35,3.35,0,0,0-2.74-.29l-5.95,2.24c-.82.31-.93.94-.25,1.41L368,410.3A3.78,3.78,0,0,0,369.36,410.87Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-146)"/><path d="M422.56,391.17c.82-.31.93-.94.24-1.41l-3.49-2.39a3.33,3.33,0,0,0-2.73-.29l-6,2.25c-.82.31-.93.94-.25,1.41l3.49,2.39a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-147)"/><path d="M449.26,389.4c.82-.31.94-.95.25-1.42l-3.25-2.22a3.39,3.39,0,0,0-2.74-.3l-6,2.26c-.82.31-.93.95-.24,1.41l3.25,2.23a3.35,3.35,0,0,0,2.74.29Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-148)"/><g id="c5Uefy-2" data-name="c5Uefy" opacity="0.19"><path d="M554.21,380.1c0,.06,0,.12,0,.18a1.33,1.33,0,0,0,.16.75,3.51,3.51,0,0,0,1.84,1.48,2.82,2.82,0,0,0,1,.25h.14l.09.14a4,4,0,0,1,.58,1.52,1.14,1.14,0,0,1,0,.37c0,.24-.24.39-.61.41l-.66,0a1.94,1.94,0,0,0-.5.08.89.89,0,0,0-.49.34,1.05,1.05,0,0,1-.32.31.89.89,0,0,1-.53.09,3.05,3.05,0,0,1-.9-.2,11.81,11.81,0,0,1-1.85-.84,9,9,0,0,1-1.64-1.08,5.29,5.29,0,0,1-1.33-1.46,1.45,1.45,0,0,1-.24-1,.79.79,0,0,1,.63-.62,3.46,3.46,0,0,1,.89-.14l.39,0a.81.81,0,0,0,.48-.18l.37-.35a1.29,1.29,0,0,1,.59-.32,2.67,2.67,0,0,1,1.36.06l.49.15Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-149)"/><path d="M550.83,380.55a2.55,2.55,0,0,1-.81-1.22.89.89,0,0,1,.42-.91,1.31,1.31,0,0,1,.31.25,1.67,1.67,0,0,1,.56,1.41.56.56,0,0,1-.35.4Z" transform="translate(-33.06 -9.17)" fill="url(#linear-gradient-150)"/></g></svg>