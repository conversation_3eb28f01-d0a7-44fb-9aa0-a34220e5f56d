<?php

declare(strict_types=1);

return [
    'accepted'             => 'The :attribute must be accepted.',
    'active_url'           => 'The :attribute is not a valid URL.',
    'after'                => 'The :attribute must be a date after :date.',
    'after_or_equal'       => 'The :attribute must be a date after or equal to :date.',
    'alpha'                => 'The :attribute must only contain letters.',
    'alpha_dash'           => 'The :attribute must only contain letters, numbers, dashes and underscores.',
    'alpha_num'            => 'The :attribute must only contain letters and numbers.',
    'array'                => 'The :attribute must be an array.',
    'before'               => 'The :attribute must be a date before :date.',
    'before_or_equal'      => 'The :attribute must be a date before or equal to :date.',
    'between'              => [
        'array'   => 'The :attribute must have between :min and :max items.',
        'file'    => 'The :attribute must be between :min and :max kilobytes.',
        'numeric' => 'The :attribute must be between :min and :max.',
        'string'  => 'The :attribute must be between :min and :max characters.',
    ],
    'boolean'              => 'The :attribute field must be true or false.',
    'confirmed'            => 'The :attribute كلمة المرور غير مطابقة',
    'date'                 => 'The :attribute is not a valid date.',
    'date_equals'          => 'The :attribute must be a date equal to :date.',
    'date_format'          => 'The :attribute does not match the format :format.',
    'different'            => 'The :attribute and :other must be different.',
    'digits'               => 'The :attribute must be :digits digits.',
    'digits_between'       => 'The :attribute must be between :min and :max digits.',
    'dimensions'           => 'The :attribute has invalid image dimensions.',
    'distinct'             => 'The :attribute field has a duplicate value.',
    'email'                => 'The :attribute برجاء ادخال الاميل بشكل صحيح',
    'ends_with'            => 'The :attribute must end with one of the following: :values.',
    'exists'               => 'The selected :attribute is invalid.',
    'file'                 => 'The :attribute must be a file.',
    'filled'               => 'The :attribute field must have a value.',
    'gt'                   => [
        'array'   => 'The :attribute must have more than :value items.',
        'file'    => 'The :attribute must be greater than :value kilobytes.',
        'numeric' => 'The :attribute must be greater than :value.',
        'string'  => 'The :attribute must be greater than :value characters.',
    ],
    'gte'                  => [
        'array'   => 'The :attribute must have :value items or more.',
        'file'    => 'The :attribute must be greater than or equal :value kilobytes.',
        'numeric' => 'The :attribute must be greater than or equal :value.',
        'string'  => 'The :attribute must be greater than or equal :value characters.',
    ],
    'image'                => 'The :attribute must be an image.',
    'in'                   => 'The selected :attribute is invalid.',
    'in_array'             => 'The :attribute field does not exist in :other.',
    'integer'              => 'The :attribute must be an integer.',
    'ip'                   => 'The :attribute must be a valid IP address.',
    'ipv4'                 => 'The :attribute must be a valid IPv4 address.',
    'ipv6'                 => 'The :attribute must be a valid IPv6 address.',
    'json'                 => 'The :attribute must be a valid JSON string.',
    'lt'                   => [
        'array'   => 'The :attribute must have less than :value items.',
        'file'    => 'The :attribute must be less than :value kilobytes.',
        'numeric' => 'The :attribute must be less than :value.',
        'string'  => 'The :attribute must be less than :value characters.',
    ],
    'lte'                  => [
        'array'   => 'The :attribute must not have more than :value items.',
        'file'    => 'The :attribute must be less than or equal :value kilobytes.',
        'numeric' => 'The :attribute must be less than or equal :value.',
        'string'  => 'The :attribute must be less than or equal :value characters.',
    ],
    'max'                  => [
        'array'   => 'The :attribute must not have more than :max items.',
        'file'    => 'The :attribute must not be greater than :max kilobytes.',
        'numeric' => 'The :attribute لقد وصلت للحد الاقصي  :max.',
        'string'  => 'The :attribute must not be greater than :max characters.',
    ],
    'mimes'                => 'The :attribute must be a file of type: :values.',
    'mimetypes'            => 'The :attribute must be a file of type: :values.',
    'min'                  => [
        'array'   => 'The :attribute must have at least :min items.',
        'file'    => 'The :attribute must be at least :min kilobytes.',
        'numeric' => 'The :attribute يجيب ان يكون على الاقل  :min.',
        'string'  => 'The :attribute يجيب ان يكون على الاقل  :min characters.',
    ],
    'multiple_of'          => 'The :attribute must be a multiple of :value.',
    'not_in'               => 'The selected :attribute is invalid.',
    'not_regex'            => 'The :attribute format is invalid.',
    'numeric'              => 'The :attribute هذا الحقل يجبب ان يكون رقم',
    'password'             => 'The password is incorrect.',
    'present'              => 'The :attribute field must be present.',
    'prohibited'           => 'The :attribute field is prohibited.',
    'prohibited_if'        => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_unless'    => 'The :attribute field is prohibited unless :other is in :values.',
    'regex'                => 'The :attribute يجيب الكتابه بشكل صحيح',
    'required'             => 'The :attribute هذا الحقل مطلوب.',
    'required_if'          => 'The :attribute field is required when :other is :value.',
    'required_unless'      => 'The :attribute field is required unless :other is in :values.',
    'required_with'        => 'The :attribute field is required when :values is present.',
    'required_with_all'    => 'The :attribute field is required when :values are present.',
    'required_without'     => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same'                 => 'The :attribute and :other must match.',
    'size'                 => [
        'array'   => 'The :attribute must contain :size items.',
        'file'    => 'The :attribute must be :size kilobytes.',
        'numeric' => 'The :attribute must be :size.',
        'string'  => 'The :attribute must be :size characters.',
    ],
    'starts_with'          => 'The :attribute must start with one of the following: :values.',
    'string'               => 'The :attribute must be a string.',
    'timezone'             => 'The :attribute must be a valid zone.',
    'unique'               => 'The :attribute هذا الحقل مكرر من قبل.',
    'uploaded'             => 'The :attribute failed to upload.',
    'url'                  => 'The :attribute format is invalid.',
    'uuid'                 => 'The :attribute must be a valid UUID.',
    'attributes'           => [
        'address'                  => 'العنوان',
        'affiliate_url'            => 'رابط الأفلييت',
        'age'                      => 'العمر',
        'amount'                   => 'الكمية',
        'announcement'             => 'إعلان',
        'area'                     => 'المنطقة',
        'audience_prize'           => 'جائزة الجمهور',
        'audience_winner'          => 'الفائز باختيار الجمهور',
        'available'                => 'مُتاح',
        'birthday'                 => 'عيد الميلاد',
        'body'                     => 'المُحتوى',
        'city'                     => 'المدينة',
        'company'                  => 'الشركة',
        'compilation'              => 'التحويل البرمجي',
        'concept'                  => 'مفهوم',
        'conditions'               => 'شروط',
        'content'                  => 'المُحتوى',
        'contest'                  => 'المسابقة',
        'country'                  => 'الدولة',
        'cover'                    => 'الغلاف',
        'created_at'               => 'تاريخ الإضافة',
        'creator'                  => 'المنشئ',
        'currency'                 => 'العملة',
        'current_password'         => 'كلمة المرور الحالية',
        'customer'                 => 'عميل',
        'date'                     => 'التاريخ',
        'date_of_birth'            => 'تاريخ الميلاد',
        'dates'                    => 'التواريخ',
        'day'                      => 'اليوم',
        'deleted_at'               => 'تاريخ الحذف',
        'description'              => 'الوصف',
        'display_type'             => 'نوع العرض',
        'district'                 => 'الحي',
        'duration'                 => 'المدة',
        'email'                    => 'البريد الالكتروني',
        'excerpt'                  => 'المُلخص',
        'filter'                   => 'تصفية',
        'finished_at'              => 'تاريخ الانتهاء',
        'first_name'               => 'الاسم الأول',
        'gender'                   => 'النوع',
        'grand_prize'              => 'الجائزة الكبرى',
        'group'                    => 'مجموعة',
        'hour'                     => 'ساعة',
        'image'                    => 'صورة',
        'image_desktop'            => 'صورة سطح المكتب',
        'image_main'               => 'الصورة الرئيسية',
        'image_mobile'             => 'صورة الجوال',
        'images'                   => 'الصور',
        'is_audience_winner'       => 'الفائز باختيار الجمهور',
        'is_hidden'                => 'مخفي',
        'is_subscribed'            => 'مشترك',
        'is_visible'               => 'مرئي',
        'is_winner'                => 'الفائز',
        'items'                    => 'العناصر',
        'key'                      => 'مفتاح',
        'last_name'                => 'اسم العائلة',
        'lesson'                   => 'الدرس',
        'line_address_1'           => 'العنوان 1',
        'line_address_2'           => 'العنوان 2',
        'login'                    => 'تسجيل الدخول',
        'message'                  => 'الرسالة',
        'middle_name'              => 'الاسم الأوسط',
        'minute'                   => 'دقيقة',
        'mobile'                   => 'الجوال',
        'month'                    => 'الشهر',
        'name'                     => 'الاسم',
        'national_code'            => 'الرمز الدولي',
        'number'                   => 'الرقم',
        'password'                 => 'كلمة المرور',
        'password_confirmation'    => 'تأكيد كلمة المرور',
        'phone'                    => 'الهاتف',
        'photo'                    => 'الصورة',
        'portfolio'                => 'ملف',
        'postal_code'              => 'الرمز البريدي',
        'preview'                  => 'معاينة',
        'price'                    => 'السعر',
        'product_id'               => 'معرف المنتج',
        'product_uid'              => 'معرف المنتج',
        'product_uuid'             => 'معرف المنتج',
        'promo_code'               => 'رمز ترويجي',
        'province'                 => 'المحافظة',
        'quantity'                 => 'الكمية',
        'reason'                   => 'سبب',
        'recaptcha_response_field' => 'حقل استجابة recaptcha',
        'referee'                  => 'حكَم',
        'referees'                 => 'حكّام',
        'reject_reason'            => 'سبب الرفض',
        'remember'                 => 'تذكير',
        'restored_at'              => 'تاريخ الاستعادة',
        'result_text_under_image'  => 'نص النتيجة أسفل الصورة',
        'role'                     => 'الصلاحية',
        'rule'                     => 'قاعدة',
        'rules'                    => 'قواعد',
        'second'                   => 'ثانية',
        'sex'                      => 'الجنس',
        'shipment'                 => 'الشحنة',
        'short_text'               => 'نص مختصر',
        'size'                     => 'الحجم',
        'skills'                   => 'مهارات',
        'slug'                     => 'نص صديق',
        'specialization'           => 'تخصص',
        'started_at'               => 'تاريخ الابتداء',
        'state'                    => 'الولاية',
        'status'                   => 'حالة',
        'street'                   => 'الشارع',
        'student'                  => 'طالب',
        'subject'                  => 'الموضوع',
        'tag'                      => 'علامة',
        'tags'                     => 'العلامات',
        'teacher'                  => 'معلّم',
        'terms'                    => 'الأحكام',
        'test_description'         => 'وصف الاختبار',
        'test_locale'              => 'لغة الاختبار',
        'test_name'                => 'اسم الاختبار',
        'text'                     => 'نص',
        'time'                     => 'الوقت',
        'title'                    => 'اللقب',
        'type'                     => 'النوع',
        'updated_at'               => 'تاريخ التحديث',
        'user'                     => 'مستخدم',
        'username'                 => 'اسم المُستخدم',
        'value'                    => 'قيمة',
        'winner'                   => 'الفائز',
        'work'                     => 'العمل',
        'year'                     => 'السنة',
    ],
];
